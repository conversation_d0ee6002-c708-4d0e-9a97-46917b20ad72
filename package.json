{"name": "mysql-typesense-sync", "version": "1.0.0", "description": "Kafka consumer that syncs MySQL changes to Typesense", "main": "consumer.js", "scripts": {"start": "node consumer.js", "dev": "nodemon consumer.js", "test": "node test-sync.js"}, "dependencies": {"kafkajs": "^2.2.4", "typesense": "^1.7.2", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["kafka", "typesense", "mysql", "debezium", "cdc"], "author": "Your Name", "license": "MIT"}