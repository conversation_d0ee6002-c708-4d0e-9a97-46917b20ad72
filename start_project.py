#!/usr/bin/env python3
"""
Project Startup Script

This script orchestrates the complete startup process for the
MySQL -> Debezium -> Kafka -> Typesense synchronization pipeline.

What this script does:
1. Checks if <PERSON><PERSON> and Docker Compose are available
2. Starts all services using Docker Compose
3. Waits for services to be ready
4. Configures Debezium connector
5. Provides instructions for running the consumer and tests
"""

import subprocess
import time
import sys
import os
import requests
from typing import List, Tuple


class ProjectStarter:
    """
    Manages the complete project startup process.
    
    This class handles:
    - Docker service management
    - Service health checking
    - Debezium configuration
    - User guidance
    """
    
    def __init__(self):
        """Initialize the project starter."""
        self.services = [
            ('MySQL', 'mysql', 'localhost:3306'),
            ('Zookeeper', 'zookeeper', 'localhost:2181'),
            ('Kafka', 'kafka', 'localhost:9092'),
            ('Debezium', 'debezium', 'localhost:8083'),
            ('Typesense', 'typesense', 'localhost:8108')
        ]
    
    def check_docker_available(self) -> bool:
        """
        Check if Docker and Docker Compose are available.
        
        Returns:
            True if both are available
        """
        print("🐳 Checking Docker availability...")
        
        try:
            # Check Docker
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                print("❌ Docker is not available")
                return False
            
            print(f"✅ {result.stdout.strip()}")
            
            # Check Docker Compose
            result = subprocess.run(['docker', 'compose', 'version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                print("❌ Docker Compose is not available")
                return False
            
            print(f"✅ {result.stdout.strip()}")
            return True
            
        except (subprocess.TimeoutExpired, FileNotFoundError) as e:
            print(f"❌ Error checking Docker: {e}")
            return False
    
    def start_docker_services(self) -> bool:
        """
        Start all Docker services using Docker Compose.
        
        Returns:
            True if services started successfully
        """
        print("🚀 Starting Docker services...")
        
        try:
            # Stop any existing services first
            print("🛑 Stopping any existing services...")
            subprocess.run(['docker', 'compose', 'down'], 
                          capture_output=True, timeout=30)
            
            # Start services in detached mode
            print("▶️  Starting services...")
            result = subprocess.run(['docker', 'compose', 'up', '-d'], 
                                  capture_output=True, text=True, timeout=120)
            
            if result.returncode != 0:
                print(f"❌ Failed to start services: {result.stderr}")
                return False
            
            print("✅ Docker services started successfully")
            return True
            
        except subprocess.TimeoutExpired:
            print("❌ Timeout starting Docker services")
            return False
        except Exception as e:
            print(f"❌ Error starting services: {e}")
            return False
    
    def check_service_health(self, service_name: str, container_name: str, endpoint: str) -> bool:
        """
        Check if a specific service is healthy and ready.
        
        Args:
            service_name: Human-readable service name
            container_name: Docker container name
            endpoint: Service endpoint to check
            
        Returns:
            True if service is healthy
        """
        print(f"🔍 Checking {service_name} health...")
        
        # First check if container is running
        try:
            result = subprocess.run(['docker', 'ps', '--filter', f'name={container_name}', '--format', '{{.Status}}'],
                                  capture_output=True, text=True, timeout=10)
            
            if 'Up' not in result.stdout:
                print(f"❌ {service_name} container is not running")
                return False
        
        except Exception as e:
            print(f"❌ Error checking {service_name} container: {e}")
            return False
        
        # Service-specific health checks
        if service_name == 'MySQL':
            return self._check_mysql_health()
        elif service_name == 'Kafka':
            return self._check_kafka_health()
        elif service_name == 'Debezium':
            return self._check_debezium_health()
        elif service_name == 'Typesense':
            return self._check_typesense_health()
        else:
            # For Zookeeper, just check if container is running
            print(f"✅ {service_name} is running")
            return True
    
    def _check_mysql_health(self) -> bool:
        """Check MySQL health by attempting connection."""
        try:
            import mysql.connector
            
            conn = mysql.connector.connect(
                host='localhost',
                port=3306,
                user='root',
                password='rootpassword',
                connection_timeout=5
            )
            conn.close()
            print("✅ MySQL is ready")
            return True
            
        except Exception:
            print("⏳ MySQL not ready yet...")
            return False
    
    def _check_kafka_health(self) -> bool:
        """Check Kafka health by listing topics."""
        try:
            result = subprocess.run([
                'docker', 'exec', 'kafka', 
                'kafka-topics', '--bootstrap-server', 'localhost:9092', '--list'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print("✅ Kafka is ready")
                return True
            else:
                print("⏳ Kafka not ready yet...")
                return False
                
        except Exception:
            print("⏳ Kafka not ready yet...")
            return False
    
    def _check_debezium_health(self) -> bool:
        """Check Debezium health via REST API."""
        try:
            response = requests.get('http://localhost:8083/connectors', timeout=5)
            if response.status_code == 200:
                print("✅ Debezium is ready")
                return True
            else:
                print("⏳ Debezium not ready yet...")
                return False
                
        except Exception:
            print("⏳ Debezium not ready yet...")
            return False
    
    def _check_typesense_health(self) -> bool:
        """Check Typesense health via REST API."""
        try:
            response = requests.get('http://localhost:8108/health', timeout=5)
            if response.status_code == 200:
                print("✅ Typesense is ready")
                return True
            else:
                print("⏳ Typesense not ready yet...")
                return False
                
        except Exception:
            print("⏳ Typesense not ready yet...")
            return False
    
    def wait_for_services(self, timeout_minutes: int = 5) -> bool:
        """
        Wait for all services to be ready.
        
        Args:
            timeout_minutes: Maximum time to wait
            
        Returns:
            True if all services are ready
        """
        print(f"⏳ Waiting for services to be ready (timeout: {timeout_minutes} minutes)...")
        
        start_time = time.time()
        timeout_seconds = timeout_minutes * 60
        
        while time.time() - start_time < timeout_seconds:
            all_ready = True
            
            for service_name, container_name, endpoint in self.services:
                if not self.check_service_health(service_name, container_name, endpoint):
                    all_ready = False
                    break
            
            if all_ready:
                print("🎉 All services are ready!")
                return True
            
            print("⏳ Some services not ready yet, waiting...")
            time.sleep(10)
        
        print("❌ Timeout waiting for services to be ready")
        return False
    
    def setup_debezium_connector(self) -> bool:
        """
        Set up the Debezium MySQL connector.
        
        Returns:
            True if setup successful
        """
        print("🔧 Setting up Debezium connector...")
        
        try:
            # Import and run the Debezium setup
            from setup_debezium import DebeziumSetup
            
            setup = DebeziumSetup()
            return setup.setup()
            
        except Exception as e:
            print(f"❌ Error setting up Debezium: {e}")
            return False
    
    def show_next_steps(self):
        """Show instructions for the next steps."""
        print("\n" + "="*60)
        print("🎉 PROJECT SETUP COMPLETE!")
        print("="*60)
        
        print("\n📋 NEXT STEPS:")
        print("\n1. Install Python dependencies:")
        print("   pip install -r requirements.txt")
        
        print("\n2. Start the Kafka consumer (in a new terminal):")
        print("   python kafka_consumer.py")
        
        print("\n3. Test the synchronization (in another terminal):")
        print("   python test_sync.py")
        
        print("\n🔍 MONITORING COMMANDS:")
        print("\n• Check Debezium connector status:")
        print("  curl http://localhost:8083/connectors/mysql-connector/status")
        
        print("\n• View Kafka topics:")
        print("  docker exec kafka kafka-topics --bootstrap-server localhost:9092 --list")
        
        print("\n• View Kafka messages:")
        print("  docker exec kafka kafka-console-consumer --bootstrap-server localhost:9092 --topic ecommerce.ecommerce.products --from-beginning")
        
        print("\n• Access Typesense:")
        print("  curl http://localhost:8108/collections")
        
        print("\n• Access MySQL:")
        print("  mysql -h localhost -u appuser -papppassword ecommerce")
        
        print("\n🛑 TO STOP ALL SERVICES:")
        print("   docker compose down")
        
        print("\n" + "="*60)
    
    def start_project(self) -> bool:
        """
        Main function to start the entire project.
        
        Returns:
            True if startup successful
        """
        print("🚀 Starting MySQL -> Debezium -> Kafka -> Typesense Project")
        print("="*60)
        
        # Step 1: Check Docker
        if not self.check_docker_available():
            print("\n❌ Please install Docker and Docker Compose first")
            return False
        
        # Step 2: Start Docker services
        if not self.start_docker_services():
            return False
        
        # Step 3: Wait for services to be ready
        if not self.wait_for_services():
            print("\n❌ Services failed to start properly")
            print("💡 Try running: docker compose logs")
            return False
        
        # Step 4: Setup Debezium connector
        if not self.setup_debezium_connector():
            print("\n❌ Failed to setup Debezium connector")
            return False
        
        # Step 5: Show next steps
        self.show_next_steps()
        
        return True


if __name__ == "__main__":
    """
    Script entry point.
    
    Run this script to start the entire project from scratch.
    """
    starter = ProjectStarter()
    
    if starter.start_project():
        print("\n✅ Project started successfully!")
        sys.exit(0)
    else:
        print("\n❌ Project startup failed!")
        sys.exit(1)
