version: '3.8'

services:
  # Zookeeper - Required by <PERSON><PERSON><PERSON> for cluster coordination
  # Manages Kafka brokers, topics, and partitions
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    hostname: zookeeper
    container_name: zookeeper
    ports:
      - "2181:2181"  # Client port for Kafka to connect
    environment:
      # Unique ID for this Zookeeper instance
      ZOOKEEPER_CLIENT_PORT: 2181
      # Heartbeat interval - how often Zookeeper checks if services are alive
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper-data:/var/lib/zookeeper/data
      - zookeeper-logs:/var/lib/zookeeper/log

  # Kafka - Message broker that receives change events from Debezium
  # Acts as the communication layer between Debezium and our consumer
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    hostname: kafka
    container_name: kafka
    depends_on:
      - zookeeper  # Ka<PERSON><PERSON> needs Zookeeper to be running first
    ports:
      - "9092:9092"  # External port for clients to connect
      - "9101:9101"  # JMX port for monitoring
    environment:
      # Unique broker ID in the Kafka cluster
      KAFKA_BROKER_ID: 1
      # Where to find Zookeeper
      <PERSON><PERSON>KA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      # How external clients can reach this Kafka broker
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      # Default replication factor for topics (1 since we have only 1 broker)
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      # JMX monitoring configuration
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost
      # Auto-create topics when producers/consumers reference them
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    volumes:
      - kafka-data:/var/lib/kafka/data

  # MySQL Database - Our source of truth
  # Configured with binlog enabled for Debezium to capture changes
  mysql:
    image: mysql:8.0
    hostname: mysql
    container_name: mysql
    ports:
      - "3306:3306"  # Standard MySQL port
    environment:
      # Root password for MySQL
      MYSQL_ROOT_PASSWORD: rootpassword
      # Create a database on startup
      MYSQL_DATABASE: ecommerce
      # Create a user for our application
      MYSQL_USER: appuser
      MYSQL_PASSWORD: apppassword
    command:
      # Enable binary logging - REQUIRED for Debezium CDC
      # binlog records all changes to the database
      - --log-bin=mysql-bin
      # Set binlog format to ROW - captures actual row changes
      - --binlog-format=ROW
      # Server ID must be unique in replication setup
      - --server-id=1
      # Enable GTID for better replication tracking
      - --gtid-mode=ON
      - --enforce-gtid-consistency=ON
      # Binlog retention (optional, for debugging)
      - --binlog-expire-logs-seconds=86400
    volumes:
      - mysql-data:/var/lib/mysql
      # Mount initialization scripts
      - ./mysql-init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Debezium Connect - Captures database changes and publishes to Kafka
  # This is the bridge between MySQL and Kafka
  debezium:
    image: debezium/connect:2.4
    hostname: debezium
    container_name: debezium
    depends_on:
      - kafka
      - mysql
    ports:
      - "8083:8083"  # REST API port for managing connectors
    environment:
      # Kafka connection details
      BOOTSTRAP_SERVERS: kafka:29092
      # Kafka Connect configuration
      GROUP_ID: 1
      CONFIG_STORAGE_TOPIC: debezium_configs
      OFFSET_STORAGE_TOPIC: debezium_offsets
      STATUS_STORAGE_TOPIC: debezium_status
      # Replication factors (1 since single broker)
      CONFIG_STORAGE_REPLICATION_FACTOR: 1
      OFFSET_STORAGE_REPLICATION_FACTOR: 1
      STATUS_STORAGE_REPLICATION_FACTOR: 1
      # Enable JSON converter for easier consumption
      KEY_CONVERTER: org.apache.kafka.connect.json.JsonConverter
      VALUE_CONVERTER: org.apache.kafka.connect.json.JsonConverter
      # Don't include schema in messages (cleaner JSON)
      KEY_CONVERTER_SCHEMAS_ENABLE: 'false'
      VALUE_CONVERTER_SCHEMAS_ENABLE: 'false'

  # Typesense - Search engine that will stay in sync with MySQL
  # Provides fast full-text search capabilities
  typesense:
    image: typesense/typesense:0.25.1
    hostname: typesense
    container_name: typesense
    ports:
      - "8108:8108"  # HTTP API port
    environment:
      # API key for authentication
      TYPESENSE_API_KEY: xyz123
      # Data directory inside container
      TYPESENSE_DATA_DIR: /data
      # Enable CORS for web applications
      TYPESENSE_ENABLE_CORS: 'true'
    volumes:
      - typesense-data:/data
    command: '--data-dir /data --api-key=xyz123 --listen-port 8108 --enable-cors'

# Named volumes for data persistence
# Without these, data would be lost when containers restart
volumes:
  zookeeper-data:
  zookeeper-logs:
  kafka-data:
  mysql-data:
  typesense-data:

# Network for service communication
# All services can reach each other by hostname
networks:
  default:
    name: cdc-network
