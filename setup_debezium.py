#!/usr/bin/env python3
"""
Debezium MySQL Connector Setup Script

This Python script configures the Debezium MySQL connector to capture
database changes and publish them to Kafka topics.

What this script does:
1. Waits for Debezium Connect service to be ready
2. Creates/updates the MySQL connector configuration
3. Verifies the connector is working properly

Why we need this:
- Debezi<PERSON> needs to be configured after all services are running
- The connector tells <PERSON><PERSON><PERSON><PERSON> which database to monitor
- It specifies which tables to track and how to format the messages
"""

import json
import requests
import time
import sys
from typing import Dict, Any


class DebeziumSetup:
    """
    Handles the setup and configuration of Debezium MySQL connector.
    
    This class manages the entire process of:
    - Checking if Debezium Connect is ready
    - Creating the MySQL connector
    - Verifying the setup worked correctly
    """
    
    def __init__(self):
        """Initialize the setup with configuration."""
        # Debezium Connect REST API endpoint
        self.debezium_url = "http://localhost:8083"
        self.connector_name = "mysql-connector"
        
        # Connector configuration
        # Each setting is explained in detail
        self.connector_config = {
            "name": self.connector_name,
            "config": {
                # Debezium MySQL connector class
                "connector.class": "io.debezium.connector.mysql.MySqlConnector",
                
                # Maximum number of tasks (parallel processing)
                "tasks.max": "1",
                
                # MySQL connection details
                "database.hostname": "mysql",
                "database.port": "3306",
                "database.user": "debezium",
                "database.password": "debezium_password",
                
                # Unique server ID for MySQL replication
                "database.server.id": "184054",
                
                # Topic prefix for Kafka topics
                # Topics will be named: ecommerce.ecommerce.tablename
                "topic.prefix": "ecommerce",
                
                # Which database to monitor
                "database.include.list": "ecommerce",
                
                # Which tables to monitor (comma-separated)
                "table.include.list": "ecommerce.products,ecommerce.categories",
                
                # Kafka configuration for schema history
                "schema.history.internal.kafka.bootstrap.servers": "kafka:29092",
                "schema.history.internal.kafka.topic": "ecommerce.schema-changes",
                
                # Include schema changes in messages
                "include.schema.changes": "true",
                
                # Snapshot mode: 'initial' means capture existing data first
                "snapshot.mode": "initial",
                
                # Message format configuration
                "key.converter": "org.apache.kafka.connect.json.JsonConverter",
                "key.converter.schemas.enable": "false",
                "value.converter": "org.apache.kafka.connect.json.JsonConverter",
                "value.converter.schemas.enable": "false",
                
                # Transform messages to extract just the data (not metadata)
                "transforms": "unwrap",
                "transforms.unwrap.type": "io.debezium.transforms.ExtractNewRecordState",
                "transforms.unwrap.drop.tombstones": "false",
                "transforms.unwrap.delete.handling.mode": "rewrite"
            }
        }
    
    def wait_for_debezium_ready(self, timeout_seconds: int = 60) -> bool:
        """
        Wait for Debezium Connect to be ready to accept requests.
        
        This function repeatedly checks if Debezium Connect is responding
        to HTTP requests. It's essential because Debezium takes time to start.
        
        Args:
            timeout_seconds: Maximum time to wait
            
        Returns:
            True if Debezium is ready, False if timeout
        """
        print("⏳ Waiting for Debezium Connect to be ready...")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout_seconds:
            try:
                # Try to get the list of connectors
                response = requests.get(f"{self.debezium_url}/connectors", timeout=5)
                
                if response.status_code == 200:
                    print("✅ Debezium Connect is ready!")
                    return True
                    
            except requests.exceptions.RequestException:
                # Connection failed, Debezium not ready yet
                pass
            
            elapsed = int(time.time() - start_time)
            print(f"⏳ Debezium not ready yet, waiting... ({elapsed}/{timeout_seconds}s)")
            time.sleep(2)
        
        print("❌ Timeout waiting for Debezium Connect to be ready")
        return False
    
    def delete_existing_connector(self) -> bool:
        """
        Delete existing connector if it exists.
        
        This ensures we start with a clean slate and can update
        the connector configuration if needed.
        
        Returns:
            True if deletion successful or connector didn't exist
        """
        try:
            # Check if connector exists
            response = requests.get(f"{self.debezium_url}/connectors/{self.connector_name}")
            
            if response.status_code == 200:
                print(f"🗑️  Deleting existing connector '{self.connector_name}'...")
                
                # Delete the connector
                delete_response = requests.delete(f"{self.debezium_url}/connectors/{self.connector_name}")
                
                if delete_response.status_code == 204:
                    print("✅ Existing connector deleted successfully")
                    time.sleep(2)  # Wait for cleanup
                    return True
                else:
                    print(f"❌ Failed to delete connector: {delete_response.status_code}")
                    return False
            else:
                print("ℹ️  No existing connector found")
                return True
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Error checking/deleting connector: {e}")
            return False
    
    def create_connector(self) -> bool:
        """
        Create the MySQL connector with our configuration.
        
        This sends the connector configuration to Debezium Connect,
        which then starts monitoring the MySQL database.
        
        Returns:
            True if creation successful
        """
        try:
            print(f"🔧 Creating MySQL connector '{self.connector_name}'...")
            
            # Send POST request to create connector
            response = requests.post(
                f"{self.debezium_url}/connectors",
                headers={"Content-Type": "application/json"},
                json=self.connector_config,
                timeout=30
            )
            
            if response.status_code == 201:
                print("✅ Connector created successfully!")
                return True
            else:
                print(f"❌ Failed to create connector: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Error creating connector: {e}")
            return False
    
    def check_connector_status(self) -> Dict[str, Any]:
        """
        Check the status of the created connector.
        
        This verifies that the connector is running properly and
        provides debugging information if there are issues.
        
        Returns:
            Connector status information
        """
        try:
            print(f"🔍 Checking connector status...")
            
            # Get connector status
            response = requests.get(f"{self.debezium_url}/connectors/{self.connector_name}/status")
            
            if response.status_code == 200:
                status = response.json()
                
                # Display status information
                connector_state = status.get('connector', {}).get('state', 'UNKNOWN')
                print(f"📊 Connector state: {connector_state}")
                
                # Check task status
                tasks = status.get('tasks', [])
                for i, task in enumerate(tasks):
                    task_state = task.get('state', 'UNKNOWN')
                    print(f"📋 Task {i} state: {task_state}")
                    
                    if task_state == 'FAILED':
                        print(f"❌ Task {i} error: {task.get('trace', 'No error details')}")
                
                return status
            else:
                print(f"❌ Failed to get connector status: {response.status_code}")
                return {}
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Error checking connector status: {e}")
            return {}
    
    def setup(self) -> bool:
        """
        Main setup function that orchestrates the entire process.
        
        This function:
        1. Waits for Debezium to be ready
        2. Deletes any existing connector
        3. Creates the new connector
        4. Verifies it's working
        
        Returns:
            True if setup successful
        """
        print("🚀 Starting Debezium MySQL connector setup...")
        
        # Step 1: Wait for Debezium to be ready
        if not self.wait_for_debezium_ready():
            return False
        
        # Step 2: Delete existing connector if it exists
        if not self.delete_existing_connector():
            return False
        
        # Step 3: Create the new connector
        if not self.create_connector():
            return False
        
        # Step 4: Wait a moment for initialization
        print("⏳ Waiting for connector to initialize...")
        time.sleep(5)
        
        # Step 5: Check connector status
        status = self.check_connector_status()
        
        if status:
            connector_state = status.get('connector', {}).get('state', 'UNKNOWN')
            if connector_state == 'RUNNING':
                print("🎉 Setup completed successfully!")
                print("📡 The connector is now capturing changes from MySQL")
                print("\n💡 You can check status anytime with:")
                print(f"   curl {self.debezium_url}/connectors/{self.connector_name}/status")
                return True
            else:
                print(f"⚠️  Connector created but not running (state: {connector_state})")
                return False
        
        return False


if __name__ == "__main__":
    """
    Script entry point.
    
    Run this script after starting all Docker services to configure Debezium.
    """
    setup = DebeziumSetup()
    
    if setup.setup():
        print("\n✅ Debezium setup completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Debezium setup failed!")
        sys.exit(1)
