#!/bin/bash

# Script to configure Debezium MySQL connector
# This script waits for <PERSON><PERSON><PERSON><PERSON> to be ready and then creates the connector

echo "Waiting for Debezium Connect to be ready..."

# Function to check if <PERSON><PERSON><PERSON><PERSON> is ready
check_debezium_ready() {
    curl -s http://localhost:8083/connectors > /dev/null 2>&1
    return $?
}

# Wait up to 60 seconds for <PERSON><PERSON><PERSON><PERSON> to be ready
TIMEOUT=60
COUNTER=0

while ! check_debezium_ready; do
    if [ $COUNTER -ge $TIMEOUT ]; then
        echo "Timeout waiting for Debe<PERSON><PERSON> Connect to be ready"
        exit 1
    fi
    echo "Debezium not ready yet, waiting... ($COUNTER/$TIMEOUT)"
    sleep 2
    COUNTER=$((COUNTER + 2))
done

echo "Debezium Connect is ready!"

# Check if connector already exists
CONNECTOR_NAME="mysql-connector"
if curl -s http://localhost:8083/connectors/$CONNECTOR_NAME | grep -q "error_code"; then
    echo "Connector does not exist, creating..."
else
    echo "Connector already exists, deleting first..."
    curl -X DELETE http://localhost:8083/connectors/$CONNECTOR_NAME
    sleep 2
fi

# Create the MySQL connector
echo "Creating MySQL connector..."
curl -X POST \
  -H "Content-Type: application/json" \
  --data @debezium-config.json \
  http://localhost:8083/connectors

echo ""
echo "Connector creation request sent!"

# Wait a moment and check status
sleep 3
echo "Checking connector status..."
curl -s http://localhost:8083/connectors/$CONNECTOR_NAME/status | jq '.'

echo ""
echo "Setup complete! The connector should now be capturing changes from MySQL."
echo "You can check the status anytime with:"
echo "curl http://localhost:8083/connectors/$CONNECTOR_NAME/status"
