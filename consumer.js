// Main Kafka consumer that syncs MySQL changes to Typesense
// This is the heart of our real-time synchronization system

require('dotenv').config();
const { Kafka } = require('kafkajs');
const Typesense = require('typesense');

// Initialize Kafka client
// KafkaJS is the Node.js client for Apache Kafka
const kafka = new Kafka({
  clientId: 'typesense-sync-consumer',  // Unique identifier for this consumer
  brokers: [process.env.KAFKA_BROKERS], // Kafka broker addresses
  retry: {
    initialRetryTime: 100,    // Start with 100ms retry delay
    retries: 8                // Retry up to 8 times on failure
  }
});

// Initialize Typesense client
// Typesense is our search engine that will mirror MySQL data
const typesense = new Typesense.Client({
  nodes: [{
    host: process.env.TYPESENSE_HOST,
    port: process.env.TYPESENSE_PORT,
    protocol: process.env.TYPESENSE_PROTOCOL
  }],
  apiKey: process.env.TYPESENSE_API_KEY,
  connectionTimeoutSeconds: 2
});

// Create Kafka consumer instance
const consumer = kafka.consumer({ 
  groupId: process.env.KAFKA_GROUP_ID,  // Consumer group for load balancing
  sessionTimeout: 30000,                // How long before consumer is considered dead
  heartbeatInterval: 3000               // How often to send heartbeat to Kafka
});

// Typesense collection schemas
// These define the structure of our search collections
const COLLECTIONS = {
  products: {
    name: 'products',
    fields: [
      { name: 'id', type: 'int32', facet: false },
      { name: 'name', type: 'string', facet: false },
      { name: 'description', type: 'string', facet: false },
      { name: 'price', type: 'float', facet: true },
      { name: 'category', type: 'string', facet: true },
      { name: 'stock_quantity', type: 'int32', facet: true },
      { name: 'created_at', type: 'string', facet: false },
      { name: 'updated_at', type: 'string', facet: false }
    ],
    default_sorting_field: 'id'
  },
  categories: {
    name: 'categories',
    fields: [
      { name: 'id', type: 'int32', facet: false },
      { name: 'name', type: 'string', facet: true },
      { name: 'description', type: 'string', facet: false },
      { name: 'created_at', type: 'string', facet: false }
    ],
    default_sorting_field: 'id'
  }
};

// Initialize Typesense collections
// This function creates the search collections if they don't exist
async function initializeTypesenseCollections() {
  console.log('Initializing Typesense collections...');
  
  for (const [tableName, schema] of Object.entries(COLLECTIONS)) {
    try {
      // Try to get existing collection
      await typesense.collections(schema.name).retrieve();
      console.log(`Collection '${schema.name}' already exists`);
    } catch (error) {
      if (error.httpStatus === 404) {
        // Collection doesn't exist, create it
        console.log(`Creating collection '${schema.name}'...`);
        await typesense.collections().create(schema);
        console.log(`Collection '${schema.name}' created successfully`);
      } else {
        console.error(`Error checking collection '${schema.name}':`, error);
        throw error;
      }
    }
  }
}

// Process database change events from Debezium
// This is where the magic happens - we transform MySQL changes into Typesense operations
async function processMessage(topic, partition, message) {
  try {
    // Parse the JSON message from Kafka
    const value = JSON.parse(message.value.toString());
    
    // Extract table name from Kafka topic
    // Topic format: ecommerce.ecommerce.tablename
    const tableName = topic.split('.').pop();
    
    console.log(`Processing ${tableName} change:`, {
      operation: value.op || 'unknown',
      timestamp: new Date().toISOString()
    });

    // Handle different types of database operations
    switch (value.op) {
      case 'c':  // CREATE (INSERT)
        await handleInsert(tableName, value.after);
        break;
      case 'u':  // UPDATE
        await handleUpdate(tableName, value.after);
        break;
      case 'd':  // DELETE
        await handleDelete(tableName, value.before);
        break;
      case 'r':  // READ (initial snapshot)
        await handleInsert(tableName, value.after);
        break;
      default:
        console.log(`Unknown operation: ${value.op}`);
    }
  } catch (error) {
    console.error('Error processing message:', error);
    // In production, you might want to send this to a dead letter queue
  }
}

// Handle INSERT operations
// Adds new documents to Typesense
async function handleInsert(tableName, data) {
  if (!data) {
    console.log('No data to insert');
    return;
  }

  try {
    // Transform MySQL data to Typesense document format
    const document = transformDataForTypesense(tableName, data);
    
    // Insert document into Typesense collection
    await typesense.collections(tableName).documents().create(document);
    console.log(`Inserted document in ${tableName}:`, document.id);
  } catch (error) {
    console.error(`Error inserting into ${tableName}:`, error);
  }
}

// Handle UPDATE operations
// Updates existing documents in Typesense
async function handleUpdate(tableName, data) {
  if (!data) {
    console.log('No data to update');
    return;
  }

  try {
    // Transform MySQL data to Typesense document format
    const document = transformDataForTypesense(tableName, data);
    
    // Update document in Typesense (upsert operation)
    await typesense.collections(tableName).documents(data.id.toString()).update(document);
    console.log(`Updated document in ${tableName}:`, document.id);
  } catch (error) {
    console.error(`Error updating ${tableName}:`, error);
  }
}

// Handle DELETE operations
// Removes documents from Typesense
async function handleDelete(tableName, data) {
  if (!data || !data.id) {
    console.log('No data or ID to delete');
    return;
  }

  try {
    // Delete document from Typesense collection
    await typesense.collections(tableName).documents(data.id.toString()).delete();
    console.log(`Deleted document from ${tableName}:`, data.id);
  } catch (error) {
    if (error.httpStatus === 404) {
      console.log(`Document ${data.id} not found in ${tableName} (already deleted?)`);
    } else {
      console.error(`Error deleting from ${tableName}:`, error);
    }
  }
}

// Transform MySQL data to Typesense document format
// This function converts database rows to search documents
function transformDataForTypesense(tableName, data) {
  // Convert all values to appropriate types for Typesense
  const document = { ...data };
  
  // Ensure ID is a number
  if (document.id) {
    document.id = parseInt(document.id);
  }
  
  // Handle specific transformations per table
  if (tableName === 'products') {
    // Ensure price is a float
    if (document.price) {
      document.price = parseFloat(document.price);
    }
    
    // Ensure stock_quantity is an integer
    if (document.stock_quantity) {
      document.stock_quantity = parseInt(document.stock_quantity);
    }
  }
  
  // Convert timestamps to strings (Typesense requirement)
  if (document.created_at) {
    document.created_at = new Date(document.created_at).toISOString();
  }
  if (document.updated_at) {
    document.updated_at = new Date(document.updated_at).toISOString();
  }
  
  return document;
}

// Main function to start the consumer
async function startConsumer() {
  try {
    console.log('Starting Kafka consumer for Typesense sync...');
    
    // Initialize Typesense collections first
    await initializeTypesenseCollections();
    
    // Connect to Kafka
    await consumer.connect();
    console.log('Connected to Kafka');
    
    // Subscribe to all topics that match our pattern
    // This will capture changes from all tables we're monitoring
    await consumer.subscribe({ 
      topics: ['ecommerce.ecommerce.products', 'ecommerce.ecommerce.categories'],
      fromBeginning: true  // Process all existing messages
    });
    
    console.log('Subscribed to topics');
    
    // Start consuming messages
    await consumer.run({
      eachMessage: async ({ topic, partition, message }) => {
        await processMessage(topic, partition, message);
      },
    });
    
  } catch (error) {
    console.error('Error starting consumer:', error);
    process.exit(1);
  }
}

// Graceful shutdown handling
// This ensures we properly disconnect from Kafka when the app stops
process.on('SIGINT', async () => {
  console.log('Received SIGINT, shutting down gracefully...');
  await consumer.disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Received SIGTERM, shutting down gracefully...');
  await consumer.disconnect();
  process.exit(0);
});

// Start the consumer
startConsumer();
