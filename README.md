# MySQL to Typesense Real-Time Synchronization

This project demonstrates a complete real-time data synchronization pipeline from MySQL to Typesense using Debezium and Kafka. Every database change (INSERT, UPDATE, DELETE) is automatically reflected in the search engine.

## 🏗️ Architecture Overview

```
MySQL Database → Debezium → Kafka → Python Consumer → Typesense
```

### Components Explained

1. **MySQL Database**: Source of truth for our data
   - Configured with binary logging for change data capture
   - Contains sample ecommerce data (products, categories)

2. **Debezium**: Change Data Capture (CDC) platform
   - Monitors MySQL binary log for changes
   - Publishes change events to Kafka topics
   - Handles initial snapshots and ongoing changes

3. **Apache Kafka**: Distributed streaming platform
   - Receives change events from Debezium
   - Provides reliable message delivery
   - Enables scalable event processing

4. **Python Consumer**: Custom application
   - Consumes change events from Kafka
   - Transforms MySQL data for Typesense
   - Performs corresponding operations in search engine

5. **Typesense**: Fast search engine
   - Provides full-text search capabilities
   - Stays synchronized with MySQL data
   - Offers faceted search and filtering

## 🚀 Quick Start

### Prerequisites

- <PERSON><PERSON> and Docker Compose
- Python 3.8+
- pip (Python package manager)

### 1. Start the Project

```bash
# Clone or download the project files
# Navigate to the project directory

# Start all services and configure Debezium
python start_project.py
```

This script will:
- Start all Docker services (MySQL, Kafka, Debezium, Typesense)
- Wait for services to be ready
- Configure the Debezium MySQL connector
- Show next steps

### 2. Install Python Dependencies

```bash
pip install -r requirements.txt
```

### 3. Start the Kafka Consumer

In a new terminal:
```bash
python kafka_consumer.py
```

This starts the consumer that syncs changes to Typesense.

### 4. Test the Synchronization

In another terminal:
```bash
python test_sync.py
```

This runs comprehensive tests to verify the sync is working.

## 📊 Data Flow Explanation

### When You Insert a Product in MySQL:

1. **MySQL**: Row inserted into `products` table
2. **Binary Log**: MySQL writes change to binary log
3. **Debezium**: Reads binary log, creates change event
4. **Kafka**: Receives event on topic `ecommerce.ecommerce.products`
5. **Consumer**: Processes event, transforms data
6. **Typesense**: Document created in `products` collection

### When You Update a Product:

1. **MySQL**: Row updated in `products` table
2. **Debezium**: Captures before/after values
3. **Kafka**: Publishes update event
4. **Consumer**: Processes update, transforms data
5. **Typesense**: Document updated with new values

### When You Delete a Product:

1. **MySQL**: Row deleted from `products` table
2. **Debezium**: Captures deletion event with old values
3. **Kafka**: Publishes delete event
4. **Consumer**: Processes deletion
5. **Typesense**: Document removed from collection

## 🔧 Configuration Details

### MySQL Configuration

The MySQL container is configured with:
- Binary logging enabled (`--log-bin=mysql-bin`)
- Row-based replication (`--binlog-format=ROW`)
- GTID mode for better tracking
- Dedicated Debezium user with proper permissions

### Debezium Connector Configuration

Key settings in the connector:
- `snapshot.mode: initial` - Captures existing data first
- `table.include.list` - Specifies which tables to monitor
- `transforms: unwrap` - Extracts just the data (not metadata)

### Kafka Topics

Debezium creates these topics automatically:
- `ecommerce.ecommerce.products` - Product changes
- `ecommerce.ecommerce.categories` - Category changes
- `ecommerce.schema-changes` - Schema evolution events

### Typesense Collections

Collections are created with schemas defining:
- Field types (string, int32, float)
- Faceting capabilities for filtering
- Default sorting fields

## 🧪 Testing Commands

### Manual Database Operations

Connect to MySQL:
```bash
mysql -h localhost -u appuser -papppassword ecommerce
```

Insert a product:
```sql
INSERT INTO products (name, description, price, category, stock_quantity) 
VALUES ('New Product', 'Test description', 29.99, 'Electronics', 100);
```

### Monitor Kafka Messages

View product change events:
```bash
docker exec kafka kafka-console-consumer \
  --bootstrap-server localhost:9092 \
  --topic ecommerce.ecommerce.products \
  --from-beginning
```

### Query Typesense

Search products:
```bash
curl "http://localhost:8108/collections/products/documents/search?q=electronics&query_by=name,category" \
  -H "X-TYPESENSE-API-KEY: xyz123"
```

### Check Debezium Status

```bash
curl http://localhost:8083/connectors/mysql-connector/status
```

## 📁 Project Structure

```
├── docker-compose.yml          # All service definitions
├── mysql-init/
│   └── 01-init.sql            # Database initialization
├── requirements.txt           # Python dependencies
├── .env                      # Environment variables
├── start_project.py          # Main startup script
├── setup_debezium.py         # Debezium connector setup
├── kafka_consumer.py         # Main sync application
├── test_sync.py             # Comprehensive tests
└── README.md                # This file
```

## 🔍 Troubleshooting

### Services Won't Start

```bash
# Check service logs
docker compose logs mysql
docker compose logs debezium
docker compose logs kafka

# Restart services
docker compose down
docker compose up -d
```

### Debezium Connector Issues

```bash
# Check connector status
curl http://localhost:8083/connectors/mysql-connector/status

# Restart connector
curl -X POST http://localhost:8083/connectors/mysql-connector/restart
```

### Consumer Not Processing Messages

1. Check if Kafka topics exist:
```bash
docker exec kafka kafka-topics --bootstrap-server localhost:9092 --list
```

2. Verify messages are in Kafka:
```bash
docker exec kafka kafka-console-consumer \
  --bootstrap-server localhost:9092 \
  --topic ecommerce.ecommerce.products \
  --from-beginning
```

3. Check consumer logs for errors

### Typesense Connection Issues

```bash
# Check Typesense health
curl http://localhost:8108/health

# List collections
curl http://localhost:8108/collections \
  -H "X-TYPESENSE-API-KEY: xyz123"
```

## 🛑 Stopping the Project

```bash
# Stop all services
docker compose down

# Remove volumes (deletes all data)
docker compose down -v
```

## 🎯 Key Learning Points

1. **Change Data Capture**: How Debezium captures database changes
2. **Event Streaming**: How Kafka handles real-time data streams
3. **Data Transformation**: Converting database formats for search
4. **Error Handling**: Graceful handling of failures and retries
5. **Monitoring**: Tracking the health of distributed systems

This project demonstrates enterprise-grade patterns for real-time data synchronization that can be scaled for production use.
