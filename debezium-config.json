{"name": "mysql-connector", "config": {"connector.class": "io.debezium.connector.mysql.MySqlConnector", "tasks.max": "1", "database.hostname": "mysql", "database.port": "3306", "database.user": "debezium", "database.password": "debezium_password", "database.server.id": "184054", "topic.prefix": "ecommerce", "database.include.list": "ecommerce", "table.include.list": "ecommerce.products,ecommerce.categories", "schema.history.internal.kafka.bootstrap.servers": "kafka:29092", "schema.history.internal.kafka.topic": "ecommerce.schema-changes", "include.schema.changes": "true", "snapshot.mode": "initial", "key.converter": "org.apache.kafka.connect.json.JsonConverter", "key.converter.schemas.enable": "false", "value.converter": "org.apache.kafka.connect.json.JsonConverter", "value.converter.schemas.enable": "false", "transforms": "unwrap", "transforms.unwrap.type": "io.debezium.transforms.ExtractNewRecordState", "transforms.unwrap.drop.tombstones": "false", "transforms.unwrap.delete.handling.mode": "rewrite"}}