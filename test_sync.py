#!/usr/bin/env python3
"""
Test Script for MySQL to Typesense Synchronization

This script tests the complete data flow:
MySQL -> Debezium -> Kafka -> Consumer -> Typesense

It performs various database operations and verifies that
the changes are properly synchronized to Typesense.
"""

import mysql.connector
import typesense
import time
import json
import sys
from datetime import datetime
from typing import List, Dict, Any


class SyncTester:
    """
    Tests the complete synchronization pipeline.
    
    This class:
    1. Connects to MySQL and Typesense
    2. Performs database operations (INSERT, UPDATE, DELETE)
    3. Verifies that changes appear in Typesense
    4. Provides detailed feedback on the sync process
    """
    
    def __init__(self):
        """Initialize connections to MySQL and Typesense."""
        print("🔧 Initializing test connections...")
        
        # MySQL connection configuration
        self.mysql_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'appuser',
            'password': 'apppassword',
            'database': 'ecommerce'
        }
        
        # Typesense client configuration
        self.typesense_client = typesense.Client({
            'nodes': [{
                'host': 'localhost',
                'port': '8108',
                'protocol': 'http'
            }],
            'api_key': 'xyz123',
            'connection_timeout_seconds': 2
        })
        
        self.mysql_conn = None
        
    def connect_mysql(self) -> bool:
        """
        Connect to MySQL database.
        
        Returns:
            True if connection successful
        """
        try:
            self.mysql_conn = mysql.connector.connect(**self.mysql_config)
            print("✅ Connected to MySQL")
            return True
        except mysql.connector.Error as e:
            print(f"❌ Failed to connect to MySQL: {e}")
            return False
    
    def test_typesense_connection(self) -> bool:
        """
        Test connection to Typesense.
        
        Returns:
            True if connection successful
        """
        try:
            # Try to get collections list
            collections = self.typesense_client.collections.retrieve()
            print("✅ Connected to Typesense")
            print(f"📊 Found {len(collections)} collections")
            return True
        except Exception as e:
            print(f"❌ Failed to connect to Typesense: {e}")
            return False
    
    def wait_for_sync(self, seconds: int = 5):
        """
        Wait for synchronization to complete.
        
        Args:
            seconds: Number of seconds to wait
        """
        print(f"⏳ Waiting {seconds} seconds for synchronization...")
        time.sleep(seconds)
    
    def insert_test_product(self) -> int:
        """
        Insert a test product into MySQL.
        
        Returns:
            ID of the inserted product
        """
        try:
            cursor = self.mysql_conn.cursor()
            
            # Insert a new product
            insert_query = """
            INSERT INTO products (name, description, price, category, stock_quantity)
            VALUES (%s, %s, %s, %s, %s)
            """
            
            test_product = (
                "Test Product",
                "This is a test product for sync verification",
                99.99,
                "Electronics",
                10
            )
            
            cursor.execute(insert_query, test_product)
            self.mysql_conn.commit()
            
            product_id = cursor.lastrowid
            cursor.close()
            
            print(f"✅ Inserted test product with ID: {product_id}")
            return product_id
            
        except mysql.connector.Error as e:
            print(f"❌ Failed to insert test product: {e}")
            return 0
    
    def update_test_product(self, product_id: int) -> bool:
        """
        Update a test product in MySQL.
        
        Args:
            product_id: ID of the product to update
            
        Returns:
            True if update successful
        """
        try:
            cursor = self.mysql_conn.cursor()
            
            # Update the product
            update_query = """
            UPDATE products 
            SET name = %s, price = %s, stock_quantity = %s
            WHERE id = %s
            """
            
            updated_values = (
                "Updated Test Product",
                149.99,
                25,
                product_id
            )
            
            cursor.execute(update_query, updated_values)
            self.mysql_conn.commit()
            cursor.close()
            
            print(f"✅ Updated test product ID: {product_id}")
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ Failed to update test product: {e}")
            return False
    
    def delete_test_product(self, product_id: int) -> bool:
        """
        Delete a test product from MySQL.
        
        Args:
            product_id: ID of the product to delete
            
        Returns:
            True if deletion successful
        """
        try:
            cursor = self.mysql_conn.cursor()
            
            # Delete the product
            delete_query = "DELETE FROM products WHERE id = %s"
            cursor.execute(delete_query, (product_id,))
            self.mysql_conn.commit()
            cursor.close()
            
            print(f"✅ Deleted test product ID: {product_id}")
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ Failed to delete test product: {e}")
            return False
    
    def check_product_in_typesense(self, product_id: int) -> Dict[str, Any]:
        """
        Check if a product exists in Typesense.
        
        Args:
            product_id: ID of the product to check
            
        Returns:
            Product document if found, empty dict if not found
        """
        try:
            # Try to get the document by ID
            document = self.typesense_client.collections['products'].documents[str(product_id)].retrieve()
            print(f"✅ Found product {product_id} in Typesense")
            return document
            
        except typesense.exceptions.ObjectNotFound:
            print(f"❌ Product {product_id} not found in Typesense")
            return {}
            
        except Exception as e:
            print(f"❌ Error checking product in Typesense: {e}")
            return {}
    
    def search_products_in_typesense(self, query: str) -> List[Dict[str, Any]]:
        """
        Search for products in Typesense.
        
        Args:
            query: Search query
            
        Returns:
            List of matching products
        """
        try:
            search_parameters = {
                'q': query,
                'query_by': 'name,description,category'
            }
            
            results = self.typesense_client.collections['products'].documents.search(search_parameters)
            hits = results.get('hits', [])
            
            print(f"🔍 Search for '{query}' returned {len(hits)} results")
            return [hit['document'] for hit in hits]
            
        except Exception as e:
            print(f"❌ Error searching in Typesense: {e}")
            return []
    
    def verify_product_data(self, product_id: int, expected_name: str, expected_price: float) -> bool:
        """
        Verify that product data in Typesense matches expectations.
        
        Args:
            product_id: Product ID to check
            expected_name: Expected product name
            expected_price: Expected product price
            
        Returns:
            True if data matches expectations
        """
        document = self.check_product_in_typesense(product_id)
        
        if not document:
            return False
        
        # Check name
        if document.get('name') != expected_name:
            print(f"❌ Name mismatch: expected '{expected_name}', got '{document.get('name')}'")
            return False
        
        # Check price (with small tolerance for float comparison)
        actual_price = float(document.get('price', 0))
        if abs(actual_price - expected_price) > 0.01:
            print(f"❌ Price mismatch: expected {expected_price}, got {actual_price}")
            return False
        
        print(f"✅ Product data verified correctly")
        return True
    
    def run_comprehensive_test(self) -> bool:
        """
        Run a comprehensive test of the synchronization pipeline.
        
        This test:
        1. Inserts a product and verifies it appears in Typesense
        2. Updates the product and verifies the changes sync
        3. Deletes the product and verifies it's removed from Typesense
        
        Returns:
            True if all tests pass
        """
        print("\n🚀 Starting comprehensive synchronization test...")
        
        # Test 1: Insert and verify
        print("\n📝 Test 1: INSERT operation")
        product_id = self.insert_test_product()
        if not product_id:
            return False
        
        self.wait_for_sync(8)  # Wait longer for initial sync
        
        if not self.verify_product_data(product_id, "Test Product", 99.99):
            print("❌ INSERT test failed")
            return False
        
        print("✅ INSERT test passed")
        
        # Test 2: Update and verify
        print("\n🔄 Test 2: UPDATE operation")
        if not self.update_test_product(product_id):
            return False
        
        self.wait_for_sync(5)
        
        if not self.verify_product_data(product_id, "Updated Test Product", 149.99):
            print("❌ UPDATE test failed")
            return False
        
        print("✅ UPDATE test passed")
        
        # Test 3: Search functionality
        print("\n🔍 Test 3: SEARCH functionality")
        search_results = self.search_products_in_typesense("Updated Test")
        
        if not search_results:
            print("❌ SEARCH test failed - no results found")
            return False
        
        # Check if our product is in the search results
        found_product = False
        for result in search_results:
            if result.get('id') == product_id:
                found_product = True
                break
        
        if not found_product:
            print("❌ SEARCH test failed - product not in search results")
            return False
        
        print("✅ SEARCH test passed")
        
        # Test 4: Delete and verify
        print("\n🗑️  Test 4: DELETE operation")
        if not self.delete_test_product(product_id):
            return False
        
        self.wait_for_sync(5)
        
        # Verify product is removed from Typesense
        document = self.check_product_in_typesense(product_id)
        if document:
            print("❌ DELETE test failed - product still exists in Typesense")
            return False
        
        print("✅ DELETE test passed")
        
        return True
    
    def run_tests(self) -> bool:
        """
        Main test runner.
        
        Returns:
            True if all tests pass
        """
        print("🧪 Starting MySQL to Typesense synchronization tests...")
        
        # Test connections
        if not self.connect_mysql():
            return False
        
        if not self.test_typesense_connection():
            return False
        
        # Run comprehensive test
        success = self.run_comprehensive_test()
        
        # Cleanup
        if self.mysql_conn:
            self.mysql_conn.close()
            print("🔌 Disconnected from MySQL")
        
        return success


if __name__ == "__main__":
    """
    Script entry point.
    
    Run this script to test the complete synchronization pipeline.
    Make sure all services are running and the consumer is active.
    """
    tester = SyncTester()
    
    if tester.run_tests():
        print("\n🎉 All tests passed! Synchronization is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Check the logs above for details.")
        sys.exit(1)
