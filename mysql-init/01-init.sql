-- This script runs when MySQL container starts for the first time
-- It creates our sample database structure

-- Use the ecommerce database (already created by environment variable)
USE ecommerce;

-- Create products table
-- This will be our main table that <PERSON><PERSON><PERSON><PERSON> will monitor for changes
CREATE TABLE products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    category VARCHAR(100),
    stock_quantity INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create categories table for reference
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample categories
INSERT INTO categories (name, description) VALUES
('Electronics', 'Electronic devices and gadgets'),
('Books', 'Physical and digital books'),
('Clothing', 'Apparel and accessories'),
('Home & Garden', 'Home improvement and gardening items');

-- Insert sample products
-- These will be the initial data that gets synced to Typesense
INSERT INTO products (name, description, price, category, stock_quantity) VALUES
('iPhone 15', 'Latest Apple smartphone with advanced features', 999.99, 'Electronics', 50),
('MacBook Pro', 'High-performance laptop for professionals', 2499.99, 'Electronics', 25),
('The Great Gatsby', 'Classic American novel by F. Scott Fitzgerald', 12.99, 'Books', 100),
('Wireless Headphones', 'Noise-cancelling Bluetooth headphones', 199.99, 'Electronics', 75),
('Cotton T-Shirt', 'Comfortable 100% cotton t-shirt', 24.99, 'Clothing', 200),
('Garden Hose', '50ft expandable garden hose', 39.99, 'Home & Garden', 30);

-- Create a user for Debezium with proper permissions
-- Debezium needs specific privileges to read binlog and table metadata
CREATE USER 'debezium'@'%' IDENTIFIED BY 'debezium_password';

-- Grant necessary permissions for Debezium CDC
-- SELECT: Read table data for initial snapshot
-- RELOAD: Flush tables and reload privileges
-- SHOW DATABASES: List available databases
-- REPLICATION SLAVE: Read binlog events
-- REPLICATION CLIENT: Get binlog position and server info
GRANT SELECT, RELOAD, SHOW DATABASES, REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO 'debezium'@'%';

-- Grant specific permissions on our database
GRANT ALL PRIVILEGES ON ecommerce.* TO 'debezium'@'%';

-- Apply the permission changes
FLUSH PRIVILEGES;

-- Show the tables we created (for verification)
SHOW TABLES;

-- Display sample data
SELECT 'Products table:' as info;
SELECT * FROM products;

SELECT 'Categories table:' as info;
SELECT * FROM categories;
