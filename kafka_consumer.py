#!/usr/bin/env python3
"""
MySQL to Typesense Sync via Kafka Consumer

This is the main application that consumes database change events from Kafka
(produced by Debezium) and synchronizes them with Typesense search engine.

Data Flow:
MySQL -> Debezium -> Kafka -> This Consumer -> Typesense

Each line is explained in detail to understand the complete workflow.
"""

import json
import logging
import os
import signal
import sys
import time
from datetime import datetime
from typing import Dict, Any, Optional

# Kafka client for consuming messages from Kafka topics
from confluent_kafka import Consumer, KafkaError, KafkaException

# Typesense client for search engine operations
import typesense

# Environment variable management
from dotenv import load_dotenv

# Load environment variables from .env file
# This allows us to configure the application without hardcoding values
load_dotenv()

# Configure logging with colors for better readability
# Logging helps us track what's happening in real-time
import colorlog

# Create a colored log formatter
# This makes it easier to distinguish between different log levels
handler = colorlog.StreamHandler()
handler.setFormatter(colorlog.ColoredFormatter(
    '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
))

# Set up the main logger
logger = colorlog.getLogger(__name__)
logger.addHandler(handler)
logger.setLevel(logging.INFO)


class TypesenseSync:
    """
    Main class that handles synchronization between Kafka and Typesense.
    
    This class:
    1. Connects to Kafka to consume database change events
    2. Processes different types of database operations (INSERT, UPDATE, DELETE)
    3. Transforms MySQL data format to Typesense document format
    4. Performs corresponding operations in Typesense
    """
    
    def __init__(self):
        """
        Initialize the synchronization service.
        
        This constructor:
        - Sets up Kafka consumer configuration
        - Initializes Typesense client
        - Defines collection schemas for search
        """
        logger.info("Initializing TypesenseSync service...")
        
        # Kafka configuration
        # These settings determine how we connect to and consume from Kafka
        self.kafka_config = {
            'bootstrap.servers': os.getenv('KAFKA_BROKERS', 'localhost:9092'),
            'group.id': os.getenv('KAFKA_GROUP_ID', 'typesense-sync-group'),
            'auto.offset.reset': 'earliest',  # Start from beginning if no offset stored
            'enable.auto.commit': True,       # Automatically commit message offsets
            'auto.commit.interval.ms': 1000,  # Commit every 1 second
        }
        
        # Typesense client configuration
        # This connects us to the search engine
        self.typesense_client = typesense.Client({
            'nodes': [{
                'host': os.getenv('TYPESENSE_HOST', 'localhost'),
                'port': os.getenv('TYPESENSE_PORT', '8108'),
                'protocol': os.getenv('TYPESENSE_PROTOCOL', 'http')
            }],
            'api_key': os.getenv('TYPESENSE_API_KEY', 'xyz123'),
            'connection_timeout_seconds': 2
        })
        
        # Define Typesense collection schemas
        # These schemas define the structure of our search collections
        # Each field has a type and whether it can be used for faceting (filtering)
        self.collection_schemas = {
            'products': {
                'name': 'products',
                'fields': [
                    {'name': 'id', 'type': 'int32', 'facet': False},
                    {'name': 'name', 'type': 'string', 'facet': False},
                    {'name': 'description', 'type': 'string', 'facet': False},
                    {'name': 'price', 'type': 'float', 'facet': True},
                    {'name': 'category', 'type': 'string', 'facet': True},
                    {'name': 'stock_quantity', 'type': 'int32', 'facet': True},
                    {'name': 'created_at', 'type': 'string', 'facet': False},
                    {'name': 'updated_at', 'type': 'string', 'facet': False}
                ],
                'default_sorting_field': 'id'
            },
            'categories': {
                'name': 'categories',
                'fields': [
                    {'name': 'id', 'type': 'int32', 'facet': False},
                    {'name': 'name', 'type': 'string', 'facet': True},
                    {'name': 'description', 'type': 'string', 'facet': False},
                    {'name': 'created_at', 'type': 'string', 'facet': False}
                ],
                'default_sorting_field': 'id'
            }
        }
        
        # Kafka consumer instance (will be initialized later)
        self.consumer = None
        
        # Flag to control the main loop
        self.running = True
        
        logger.info("TypesenseSync initialized successfully")
    
    def setup_signal_handlers(self):
        """
        Set up signal handlers for graceful shutdown.
        
        This ensures that when we stop the application (Ctrl+C),
        it properly disconnects from Kafka and Typesense.
        """
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, shutting down gracefully...")
            self.running = False
            if self.consumer:
                self.consumer.close()
            sys.exit(0)
        
        # Handle Ctrl+C (SIGINT) and termination (SIGTERM)
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def initialize_typesense_collections(self):
        """
        Create Typesense collections if they don't exist.
        
        This function:
        1. Checks if each collection exists
        2. Creates missing collections with proper schema
        3. Handles errors gracefully
        
        Why this is needed:
        - Typesense requires collections to exist before we can add documents
        - Collections define the structure and search capabilities
        """
        logger.info("Initializing Typesense collections...")
        
        for table_name, schema in self.collection_schemas.items():
            try:
                # Try to retrieve existing collection
                self.typesense_client.collections[schema['name']].retrieve()
                logger.info(f"Collection '{schema['name']}' already exists")
                
            except typesense.exceptions.ObjectNotFound:
                # Collection doesn't exist, create it
                logger.info(f"Creating collection '{schema['name']}'...")
                self.typesense_client.collections.create(schema)
                logger.info(f"Collection '{schema['name']}' created successfully")
                
            except Exception as e:
                logger.error(f"Error with collection '{schema['name']}': {e}")
                raise
    
    def transform_mysql_to_typesense(self, table_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform MySQL row data to Typesense document format.
        
        This function:
        1. Converts data types to match Typesense requirements
        2. Handles special fields like timestamps
        3. Ensures data integrity
        
        Args:
            table_name: Name of the MySQL table
            data: Raw data from MySQL (via Debezium)
            
        Returns:
            Transformed document ready for Typesense
        """
        if not data:
            return {}
        
        # Create a copy to avoid modifying original data
        document = data.copy()
        
        # Ensure ID is an integer (required by Typesense)
        if 'id' in document and document['id'] is not None:
            document['id'] = int(document['id'])
        
        # Handle products table specific transformations
        if table_name == 'products':
            # Convert price to float for numerical operations
            if 'price' in document and document['price'] is not None:
                document['price'] = float(document['price'])
            
            # Convert stock_quantity to integer
            if 'stock_quantity' in document and document['stock_quantity'] is not None:
                document['stock_quantity'] = int(document['stock_quantity'])
        
        # Convert timestamps to ISO format strings
        # Typesense requires timestamps as strings
        for timestamp_field in ['created_at', 'updated_at']:
            if timestamp_field in document and document[timestamp_field] is not None:
                if isinstance(document[timestamp_field], str):
                    # Already a string, ensure it's in ISO format
                    try:
                        dt = datetime.fromisoformat(document[timestamp_field].replace('Z', '+00:00'))
                        document[timestamp_field] = dt.isoformat()
                    except:
                        # Keep original if parsing fails
                        pass
                else:
                    # Convert datetime object to ISO string
                    document[timestamp_field] = document[timestamp_field].isoformat()
        
        # Remove None values (Typesense doesn't like them)
        document = {k: v for k, v in document.items() if v is not None}
        
        return document
    
    def handle_insert_operation(self, table_name: str, data: Dict[str, Any]):
        """
        Handle INSERT operations from MySQL.
        
        When a new row is added to MySQL:
        1. Transform the data for Typesense
        2. Create a new document in the corresponding collection
        
        Args:
            table_name: MySQL table name
            data: New row data
        """
        if not data:
            logger.warning("No data provided for insert operation")
            return
        
        try:
            # Transform MySQL data to Typesense format
            document = self.transform_mysql_to_typesense(table_name, data)
            
            if not document:
                logger.warning(f"Empty document after transformation for {table_name}")
                return
            
            # Insert document into Typesense collection
            result = self.typesense_client.collections[table_name].documents.create(document)
            logger.info(f"✅ Inserted document in {table_name}: ID {document.get('id')}")
            
        except Exception as e:
            logger.error(f"❌ Error inserting into {table_name}: {e}")
            logger.error(f"Data: {data}")
    
    def handle_update_operation(self, table_name: str, data: Dict[str, Any]):
        """
        Handle UPDATE operations from MySQL.
        
        When a row is updated in MySQL:
        1. Transform the updated data
        2. Update the corresponding document in Typesense
        
        Args:
            table_name: MySQL table name
            data: Updated row data
        """
        if not data or 'id' not in data:
            logger.warning("No data or ID provided for update operation")
            return
        
        try:
            # Transform MySQL data to Typesense format
            document = self.transform_mysql_to_typesense(table_name, data)
            
            if not document:
                logger.warning(f"Empty document after transformation for {table_name}")
                return
            
            # Update document in Typesense (upsert operation)
            doc_id = str(document['id'])
            result = self.typesense_client.collections[table_name].documents[doc_id].update(document)
            logger.info(f"🔄 Updated document in {table_name}: ID {document.get('id')}")
            
        except Exception as e:
            logger.error(f"❌ Error updating {table_name}: {e}")
            logger.error(f"Data: {data}")
    
    def handle_delete_operation(self, table_name: str, data: Dict[str, Any]):
        """
        Handle DELETE operations from MySQL.
        
        When a row is deleted from MySQL:
        1. Extract the ID from the deleted row data
        2. Delete the corresponding document from Typesense
        
        Args:
            table_name: MySQL table name
            data: Deleted row data (contains the ID)
        """
        if not data or 'id' not in data:
            logger.warning("No data or ID provided for delete operation")
            return
        
        try:
            doc_id = str(data['id'])
            
            # Delete document from Typesense collection
            self.typesense_client.collections[table_name].documents[doc_id].delete()
            logger.info(f"🗑️  Deleted document from {table_name}: ID {data['id']}")
            
        except typesense.exceptions.ObjectNotFound:
            logger.warning(f"Document {data['id']} not found in {table_name} (already deleted?)")
            
        except Exception as e:
            logger.error(f"❌ Error deleting from {table_name}: {e}")
            logger.error(f"Data: {data}")
    
    def process_kafka_message(self, message):
        """
        Process a single Kafka message containing a database change event.

        This is the core function that:
        1. Parses the Debezium message format
        2. Extracts operation type and data
        3. Routes to appropriate handler function

        Args:
            message: Kafka message from Debezium
        """
        try:
            # Extract topic name to determine table
            topic = message.topic()
            # Topic format: ecommerce.ecommerce.tablename
            table_name = topic.split('.')[-1]

            # Get the message value and parse JSON
            message_value = message.value().decode('utf-8')
            change_event = json.loads(message_value)

            # Extract operation type from Debezium message
            operation = change_event.get('op', 'unknown')

            logger.info(f"📨 Processing {table_name} operation: {operation}")

            # Route to appropriate handler based on operation type
            if operation == 'c':  # CREATE (INSERT)
                self.handle_insert_operation(table_name, change_event.get('after'))

            elif operation == 'u':  # UPDATE
                self.handle_update_operation(table_name, change_event.get('after'))

            elif operation == 'd':  # DELETE
                self.handle_delete_operation(table_name, change_event.get('before'))

            elif operation == 'r':  # READ (initial snapshot)
                # Treat snapshot reads as inserts
                self.handle_insert_operation(table_name, change_event.get('after'))

            else:
                logger.warning(f"Unknown operation type: {operation}")

        except Exception as e:
            logger.error(f"❌ Error processing message: {e}")
            logger.error(f"Message: {message.value()}")
    
    def start_consuming(self):
        """
        Start the main Kafka consumer loop.
        
        This function:
        1. Creates Kafka consumer
        2. Subscribes to relevant topics
        3. Continuously polls for messages
        4. Processes each message
        """
        logger.info("🚀 Starting Kafka consumer...")
        
        try:
            # Create Kafka consumer with our configuration
            self.consumer = Consumer(self.kafka_config)

            # Subscribe to topics that contain our table changes
            # These topics are created automatically by Debezium
            topics = [
                'ecommerce.ecommerce.products',
                'ecommerce.ecommerce.categories'
            ]

            self.consumer.subscribe(topics)
            logger.info(f"📡 Subscribed to topics: {topics}")

            # Main consumption loop
            logger.info("🔄 Starting message consumption loop...")

            while self.running:
                try:
                    # Poll for new messages (timeout after 1 second)
                    msg = self.consumer.poll(timeout=1.0)

                    if msg is None:
                        continue

                    if msg.error():
                        if msg.error().code() == KafkaError._PARTITION_EOF:
                            # End of partition event
                            continue
                        else:
                            logger.error(f"Kafka error: {msg.error()}")
                            continue

                    # Process the message
                    self.process_kafka_message(msg)

                except KafkaException as e:
                    logger.error(f"Kafka exception: {e}")
                    time.sleep(5)  # Wait before retrying
                    
        except Exception as e:
            logger.error(f"❌ Fatal error in consumer: {e}")
            raise
        
        finally:
            if self.consumer:
                logger.info("Closing Kafka consumer...")
                self.consumer.close()
    
    def run(self):
        """
        Main entry point to start the synchronization service.
        
        This function orchestrates the entire startup process:
        1. Set up signal handlers for graceful shutdown
        2. Initialize Typesense collections
        3. Start Kafka consumer
        """
        logger.info("🎯 Starting MySQL to Typesense synchronization service")
        
        try:
            # Set up graceful shutdown
            self.setup_signal_handlers()
            
            # Initialize Typesense collections
            self.initialize_typesense_collections()
            
            # Start consuming messages
            self.start_consuming()
            
        except KeyboardInterrupt:
            logger.info("👋 Received keyboard interrupt, shutting down...")
        except Exception as e:
            logger.error(f"❌ Fatal error: {e}")
            sys.exit(1)


if __name__ == "__main__":
    """
    Application entry point.
    
    When you run this script directly, it creates and starts the sync service.
    """
    # Create and run the synchronization service
    sync_service = TypesenseSync()
    sync_service.run()
